import sqlite3 from 'sqlite3';
import bcrypt from 'bcryptjs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const dbPath = path.join(__dirname, 'handyman.db');
const db = new (sqlite3.verbose().Database)(dbPath, (err) => {
  if (err) {
    console.error('Error opening database:', err);
  } else {
    console.log('Connected to SQLite database');
  }
});

// Initialize database
const initDatabase = () => {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Create users table
      db.run(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          role TEXT NOT NULL CHECK(role IN ('admin', 'employee')),
          full_name TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('Error creating users table:', err);
          reject(err);
          return;
        }

        // Create tasks table
        db.run(`
          CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_name TEXT NOT NULL,
            customer_phone TEXT NOT NULL,
            customer_postcode TEXT NOT NULL,
            customer_email TEXT,
            service_type TEXT NOT NULL,
            service_description TEXT NOT NULL,
            start_date DATE NOT NULL,
            estimated_end_date DATE NOT NULL,
            actual_end_date DATE,
            service_fee DECIMAL(10,2),
            status TEXT NOT NULL DEFAULT 'pending' CHECK(status IN ('pending', 'assigned', 'in_progress', 'completed', 'cancelled')),
            assigned_employee_id INTEGER,
            created_by INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (assigned_employee_id) REFERENCES users(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
          )
        `, (err) => {
          if (err) {
            console.error('Error creating tasks table:', err);
            reject(err);
            return;
          }

          // Create task_photos table
          db.run(`
            CREATE TABLE IF NOT EXISTS task_photos (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              task_id INTEGER NOT NULL,
              photo_type TEXT NOT NULL CHECK(photo_type IN ('start', 'end', 'progress')),
              file_path TEXT NOT NULL,
              file_name TEXT NOT NULL,
              upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
              exif_data TEXT,
              gps_latitude REAL,
              gps_longitude REAL,
              uploaded_by INTEGER NOT NULL,
              FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
              FOREIGN KEY (uploaded_by) REFERENCES users(id)
            )
          `, (err) => {
            if (err) {
              console.error('Error creating task_photos table:', err);
              reject(err);
              return;
            }

            // Create task_work_sessions table for time tracking
            db.run(`
              CREATE TABLE IF NOT EXISTS task_work_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id INTEGER NOT NULL,
                employee_id INTEGER NOT NULL,
                start_time DATETIME NOT NULL,
                end_time DATETIME,
                work_date DATE NOT NULL,
                total_hours DECIMAL(4,2),
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
                FOREIGN KEY (employee_id) REFERENCES users(id)
              )
            `, (err) => {
              if (err) {
                console.error('Error creating task_work_sessions table:', err);
                reject(err);
                return;
              }

              // Create employee_attendance table for clock in/out tracking
              db.run(`
                CREATE TABLE IF NOT EXISTS employee_attendance (
                  id INTEGER PRIMARY KEY AUTOINCREMENT,
                  employee_id INTEGER NOT NULL,
                  clock_in_time DATETIME,
                  clock_out_time DATETIME,
                  work_date DATE NOT NULL,
                  status TEXT NOT NULL DEFAULT 'clocked_out' CHECK(status IN ('clocked_in', 'clocked_out')),
                  total_work_hours DECIMAL(4,2),
                  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (employee_id) REFERENCES users(id),
                  UNIQUE(employee_id, work_date)
                )
              `, (err) => {
                if (err) {
                  console.error('Error creating employee_attendance table:', err);
                  reject(err);
                  return;
                }

                // Check if users already exist
                db.get("SELECT COUNT(*) as count FROM users", (err, row) => {
                  if (err) {
                    reject(err);
                    return;
                  }

                  if (row.count === 0) {
                    insertDefaultUsers().then(() => {
                      insertDefaultTasks().then(resolve).catch(reject);
                    }).catch(reject);
                  } else {
                    console.log('Users already exist in database');
                    // Check if tasks exist, if not create default tasks
                    db.get("SELECT COUNT(*) as count FROM tasks", (err, row) => {
                      if (err) {
                        reject(err);
                        return;
                      }

                      if (row.count === 0) {
                        insertDefaultTasks().then(resolve).catch(reject);
                      } else {
                        console.log('Tasks already exist in database');
                        resolve();
                      }
                    });
                  }
                });
              });
            });
          });
        });
      });
    });
  });
};

// Insert default users
const insertDefaultUsers = async () => {
  const defaultPassword = 'password123';
  const hashedPassword = await bcrypt.hash(defaultPassword, 10);
  
  const users = [
    { username: 'admin', password: hashedPassword, role: 'admin', full_name: 'Administrator' },
    { username: 'tom', password: hashedPassword, role: 'employee', full_name: 'Tom Smith' },
    { username: 'john', password: hashedPassword, role: 'employee', full_name: 'John Johnson' },
    { username: 'mike', password: hashedPassword, role: 'employee', full_name: 'Mike Wilson' },
    { username: 'david', password: hashedPassword, role: 'employee', full_name: 'David Brown' },
    { username: 'james', password: hashedPassword, role: 'employee', full_name: 'James Davis' },
    { username: 'robert', password: hashedPassword, role: 'employee', full_name: 'Robert Miller' },
    { username: 'william', password: hashedPassword, role: 'employee', full_name: 'William Taylor' },
    { username: 'richard', password: hashedPassword, role: 'employee', full_name: 'Richard Anderson' },
    { username: 'charles', password: hashedPassword, role: 'employee', full_name: 'Charles Thomas' },
    { username: 'daniel', password: hashedPassword, role: 'employee', full_name: 'Daniel Jackson' }
  ];
  
  return new Promise((resolve, reject) => {
    const stmt = db.prepare("INSERT INTO users (username, password, role, full_name) VALUES (?, ?, ?, ?)");
    
    let completed = 0;
    users.forEach((user) => {
      stmt.run([user.username, user.password, user.role, user.full_name], (err) => {
        if (err) {
          console.error(`Error inserting user ${user.username}:`, err);
          reject(err);
          return;
        }
        completed++;
        if (completed === users.length) {
          stmt.finalize();
          console.log('Default users inserted successfully');
          resolve();
        }
      });
    });
  });
};

// Insert default tasks - removed demo data, only admin-created tasks will be shown
const insertDefaultTasks = async () => {
  // No default tasks - all tasks should be created by admin through the interface
  const tasks = [];

  console.log('No default tasks to insert - tasks will be created by admin');
  return Promise.resolve();
};

// User authentication
const authenticateUser = (username, password) => {
  return new Promise((resolve, reject) => {
    db.get(
      "SELECT * FROM users WHERE username = ?",
      [username],
      async (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        
        if (!row) {
          resolve(null);
          return;
        }
        
        const isValid = await bcrypt.compare(password, row.password);
        if (isValid) {
          resolve({
            id: row.id,
            username: row.username,
            role: row.role,
            full_name: row.full_name
          });
        } else {
          resolve(null);
        }
      }
    );
  });
};

// Get all users (admin only)
const getAllUsers = () => {
  return new Promise((resolve, reject) => {
    db.all(
      "SELECT id, username, role, full_name, created_at FROM users ORDER BY role, username",
      (err, rows) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(rows);
      }
    );
  });
};

// Task management functions
const getAllTasks = (filters = {}) => {
  return new Promise((resolve, reject) => {
    let query = `
      SELECT t.*, u.full_name as assigned_employee_name, c.full_name as created_by_name
      FROM tasks t
      LEFT JOIN users u ON t.assigned_employee_id = u.id
      LEFT JOIN users c ON t.created_by = c.id
      WHERE 1=1
    `;
    const params = [];

    // Employee access filter - can see assigned tasks or unassigned tasks
    if (filters.employee_access) {
      if (filters.employee_week_limit) {
        // For employees: assigned tasks (any date) OR unassigned tasks within 1 week
        query += ' AND (t.assigned_employee_id = ? OR (t.assigned_employee_id IS NULL AND t.status = ? AND t.start_date >= ? AND t.start_date <= ?))';
        params.push(filters.employee_access);
        params.push('pending');
        params.push(filters.employee_week_limit.start);
        params.push(filters.employee_week_limit.end);
      } else {
        // Standard employee filter
        query += ' AND (t.assigned_employee_id = ? OR (t.assigned_employee_id IS NULL AND t.status = ?))';
        params.push(filters.employee_access);
        params.push('pending');
      }
    }

    if (filters.status) {
      query += ' AND t.status = ?';
      params.push(filters.status);
    }

    if (filters.assigned_employee_id && !filters.employee_access) {
      query += ' AND t.assigned_employee_id = ?';
      params.push(filters.assigned_employee_id);
    }

    if (filters.start_date) {
      // For multi-day tasks, check if the task overlaps with the selected date range
      query += ' AND (t.start_date <= ? AND t.estimated_end_date >= ?)';
      params.push(filters.end_date || filters.start_date);
      params.push(filters.start_date);
    }

    if (filters.end_date && !filters.start_date) {
      query += ' AND (t.start_date <= ? AND t.estimated_end_date >= ?)';
      params.push(filters.end_date);
      params.push(filters.end_date);
    }

    query += ' ORDER BY t.start_date DESC, t.created_at DESC';

    db.all(query, params, (err, rows) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(rows);
    });
  });
};

const getTaskById = (id) => {
  return new Promise((resolve, reject) => {
    const query = `
      SELECT t.*, u.full_name as assigned_employee_name, c.full_name as created_by_name
      FROM tasks t
      LEFT JOIN users u ON t.assigned_employee_id = u.id
      LEFT JOIN users c ON t.created_by = c.id
      WHERE t.id = ?
    `;

    db.get(query, [id], (err, row) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(row);
    });
  });
};

const createTask = (taskData) => {
  return new Promise((resolve, reject) => {
    const query = `
      INSERT INTO tasks (
        customer_name, customer_phone, customer_postcode, customer_email,
        service_type, service_description, start_date, estimated_end_date,
        service_fee, status, assigned_employee_id, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    db.run(query, [
      taskData.customer_name,
      taskData.customer_phone,
      taskData.customer_postcode,
      taskData.customer_email,
      taskData.service_type,
      taskData.service_description,
      taskData.start_date,
      taskData.estimated_end_date,
      taskData.service_fee,
      taskData.status || 'pending',
      taskData.assigned_employee_id,
      taskData.created_by
    ], function(err) {
      if (err) {
        reject(err);
        return;
      }
      resolve({ id: this.lastID });
    });
  });
};

const updateTask = (id, taskData) => {
  return new Promise((resolve, reject) => {
    const fields = [];
    const params = [];

    Object.keys(taskData).forEach(key => {
      if (taskData[key] !== undefined) {
        fields.push(`${key} = ?`);
        params.push(taskData[key]);
      }
    });

    if (fields.length === 0) {
      resolve({ changes: 0 });
      return;
    }

    fields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    const query = `UPDATE tasks SET ${fields.join(', ')} WHERE id = ?`;

    db.run(query, params, function(err) {
      if (err) {
        reject(err);
        return;
      }
      resolve({ changes: this.changes });
    });
  });
};

const deleteTask = (id) => {
  return new Promise((resolve, reject) => {
    db.run('DELETE FROM tasks WHERE id = ?', [id], function(err) {
      if (err) {
        reject(err);
        return;
      }
      resolve({ changes: this.changes });
    });
  });
};

// Generate work sessions for multi-day tasks
const generateWorkSessions = (taskId, startDate, endDate, employeeId = null) => {
  return new Promise((resolve, reject) => {
    // If no employee assigned or single day task, don't create work sessions
    if (!employeeId || startDate === endDate) {
      resolve([]);
      return;
    }

    const sessions = [];
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Generate sessions for each day
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      sessions.push({
        task_id: taskId,
        employee_id: employeeId,
        work_date: d.toISOString().split('T')[0],
        start_time: d.toISOString().split('T')[0] + ' 09:00:00' // Default start time
      });
    }

    if (sessions.length === 0) {
      resolve([]);
      return;
    }

    const stmt = db.prepare(`
      INSERT INTO task_work_sessions (task_id, employee_id, work_date, start_time)
      VALUES (?, ?, ?, ?)
    `);

    let completed = 0;
    sessions.forEach(session => {
      stmt.run([session.task_id, session.employee_id, session.work_date, session.start_time], (err) => {
        if (err) {
          console.error('Error inserting work session:', err);
          reject(err);
          return;
        }

        completed++;
        if (completed === sessions.length) {
          stmt.finalize();
          resolve(sessions);
        }
      });
    });
  });
};

// Get work sessions for a task
const getTaskWorkSessions = (taskId) => {
  return new Promise((resolve, reject) => {
    const query = `
      SELECT * FROM task_work_sessions
      WHERE task_id = ?
      ORDER BY work_date ASC
    `;

    db.all(query, [taskId], (err, rows) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(rows);
    });
  });
};

// Update work session
const updateWorkSession = (sessionId, sessionData) => {
  return new Promise((resolve, reject) => {
    const fields = [];
    const params = [];

    Object.keys(sessionData).forEach(key => {
      if (sessionData[key] !== undefined) {
        fields.push(`${key} = ?`);
        params.push(sessionData[key]);
      }
    });

    if (fields.length === 0) {
      resolve({ changes: 0 });
      return;
    }

    params.push(sessionId);

    const query = `UPDATE task_work_sessions SET ${fields.join(', ')} WHERE id = ?`;

    db.run(query, params, function(err) {
      if (err) {
        reject(err);
        return;
      }
      resolve({ changes: this.changes });
    });
  });
};

// Employee attendance functions
const getEmployeeAttendance = (employeeId, workDate) => {
  return new Promise((resolve, reject) => {
    const query = `
      SELECT * FROM employee_attendance
      WHERE employee_id = ? AND work_date = ?
    `;

    db.get(query, [employeeId, workDate], (err, row) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(row);
    });
  });
};

const clockIn = (employeeId) => {
  return new Promise((resolve, reject) => {
    const now = new Date();
    const workDate = now.toISOString().split('T')[0];
    const clockInTime = now.toISOString();

    // Check if already clocked in today
    getEmployeeAttendance(employeeId, workDate).then(attendance => {
      if (attendance && attendance.status === 'clocked_in') {
        reject(new Error('Already clocked in today'));
        return;
      }

      const query = `
        INSERT OR REPLACE INTO employee_attendance
        (employee_id, work_date, clock_in_time, status, updated_at)
        VALUES (?, ?, ?, 'clocked_in', CURRENT_TIMESTAMP)
      `;

      db.run(query, [employeeId, workDate, clockInTime], function(err) {
        if (err) {
          reject(err);
          return;
        }
        resolve({ id: this.lastID, clock_in_time: clockInTime, work_date: workDate });
      });
    }).catch(reject);
  });
};

const clockOut = (employeeId) => {
  return new Promise((resolve, reject) => {
    const now = new Date();
    const workDate = now.toISOString().split('T')[0];
    const clockOutTime = now.toISOString();

    // Get today's attendance record
    getEmployeeAttendance(employeeId, workDate).then(attendance => {
      if (!attendance || attendance.status === 'clocked_out') {
        reject(new Error('Not clocked in today'));
        return;
      }

      // Get total work hours from task work sessions for today
      const sessionQuery = `
        SELECT SUM(total_hours) as total_task_hours
        FROM task_work_sessions
        WHERE employee_id = ? AND work_date = ?
      `;

      db.get(sessionQuery, [employeeId, workDate], (err, sessionRow) => {
        if (err) {
          reject(err);
          return;
        }

        const totalTaskHours = sessionRow?.total_task_hours || 0;

        const query = `
          UPDATE employee_attendance
          SET clock_out_time = ?, status = 'clocked_out', total_work_hours = ?, updated_at = CURRENT_TIMESTAMP
          WHERE employee_id = ? AND work_date = ?
        `;

        db.run(query, [clockOutTime, totalTaskHours.toFixed(2), employeeId, workDate], function(err) {
          if (err) {
            reject(err);
            return;
          }
          resolve({
            clock_out_time: clockOutTime,
            total_work_hours: totalTaskHours.toFixed(2),
            work_date: workDate
          });
        });
      });
    }).catch(reject);
  });
};

// Get attendance statistics for an employee
const getAttendanceStats = (employeeId, period = 'week') => {
  return new Promise((resolve, reject) => {
    const now = new Date();
    let startDate;

    if (period === 'week') {
      // Get start of current week (Monday)
      const dayOfWeek = now.getDay();
      const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      startDate = new Date(now);
      startDate.setDate(now.getDate() - daysToMonday);
    } else if (period === 'month') {
      // Get start of current month
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = now.toISOString().split('T')[0];

    const query = `
      SELECT
        COUNT(*) as days_worked,
        SUM(total_work_hours) as total_hours,
        AVG(total_work_hours) as avg_hours_per_day
      FROM employee_attendance
      WHERE employee_id = ?
        AND work_date >= ?
        AND work_date <= ?
        AND clock_out_time IS NOT NULL
    `;

    db.get(query, [employeeId, startDateStr, endDateStr], (err, row) => {
      if (err) {
        reject(err);
        return;
      }

      resolve({
        period,
        start_date: startDateStr,
        end_date: endDateStr,
        days_worked: row.days_worked || 0,
        total_hours: parseFloat(row.total_hours || 0).toFixed(2),
        avg_hours_per_day: parseFloat(row.avg_hours_per_day || 0).toFixed(2)
      });
    });
  });
};

// Get recent attendance records for activity display
const getRecentAttendance = (employeeId, limit = 10) => {
  return new Promise((resolve, reject) => {
    const query = `
      SELECT work_date, clock_in_time, clock_out_time, total_work_hours, status
      FROM employee_attendance
      WHERE employee_id = ?
      ORDER BY work_date DESC
      LIMIT ?
    `;

    db.all(query, [employeeId, limit], (err, rows) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(rows);
    });
  });
};

// Get daily attendance overview for admin
const getDailyAttendanceOverview = (workDate) => {
  return new Promise((resolve, reject) => {
    const today = workDate || new Date().toISOString().split('T')[0];

    const query = `
      SELECT
        u.id,
        u.full_name,
        u.username,
        ea.clock_in_time,
        ea.clock_out_time,
        ea.status,
        ea.total_work_hours
      FROM users u
      LEFT JOIN employee_attendance ea ON u.id = ea.employee_id AND ea.work_date = ?
      WHERE u.role = 'employee'
      ORDER BY u.full_name
    `;

    db.all(query, [today], (err, rows) => {
      if (err) {
        reject(err);
        return;
      }

      const attendanceData = rows.map(row => ({
        employee_id: row.id,
        employee_name: row.full_name,
        username: row.username,
        clock_in_time: row.clock_in_time,
        clock_out_time: row.clock_out_time,
        status: row.status || 'not_clocked_in',
        total_work_hours: row.total_work_hours || 0,
        work_date: today
      }));

      resolve(attendanceData);
    });
  });
};

export {
  db,
  initDatabase,
  authenticateUser,
  getAllUsers,
  getAllTasks,
  getTaskById,
  createTask,
  updateTask,
  deleteTask,
  generateWorkSessions,
  getTaskWorkSessions,
  updateWorkSession,
  getEmployeeAttendance,
  clockIn,
  clockOut,
  getAttendanceStats,
  getRecentAttendance,
  getDailyAttendanceOverview
};

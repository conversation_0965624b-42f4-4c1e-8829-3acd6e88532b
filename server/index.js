import express from 'express';
import cors from 'cors';
import jwt from 'jsonwebtoken';
import { initDatabase, authenticateUser, getAllUsers, getAllTasks, getTaskById, createTask, updateTask, deleteTask, generateWorkSessions, getTaskWorkSessions, updateWorkSession, getEmployeeAttendance, clockIn, clockOut, getAttendanceStats, getRecentAttendance, getDailyAttendanceOverview } from './database.js';

const app = express();
const PORT = process.env.PORT || 3001;
const JWT_SECRET = 'handyman-secret-key-demo-only';

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://**************:5173', 'http://localhost:5174', 'http://**************:5174'],
  credentials: true
}));
app.use(express.json());

// Initialize database on startup
initDatabase().then(() => {
  console.log('Database initialized successfully');
}).catch((error) => {
  console.error('Database initialization failed:', error);
});

// JWT middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Admin middleware
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

// Routes
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password required' });
    }

    const user = await authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const token = jwt.sign(
      { id: user.id, username: user.username, role: user.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      token,
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        full_name: user.full_name
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/profile', authenticateToken, (req, res) => {
  res.json({ user: req.user });
});

app.get('/api/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const users = await getAllUsers();
    res.json(users);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Task management endpoints
app.get('/api/tasks', authenticateToken, async (req, res) => {
  try {
    const filters = {};

    // Apply role-based filtering
    if (req.user.role === 'employee') {
      // Employees can see:
      // 1. Tasks assigned to them
      // 2. Unassigned tasks (pending status with no assigned employee) within 1 week
      filters.employee_access = req.user.id;

      // For unassigned tasks, limit to 1 week from today
      if (!req.query.start_date && !req.query.end_date) {
        const today = new Date();
        const oneWeekFromNow = new Date(today);
        oneWeekFromNow.setDate(today.getDate() + 7);

        filters.employee_week_limit = {
          start: today.toISOString().split('T')[0],
          end: oneWeekFromNow.toISOString().split('T')[0]
        };
      }
    }

    // Apply query filters
    if (req.query.status) {
      filters.status = req.query.status;
    }

    if (req.query.start_date) {
      filters.start_date = req.query.start_date;
    }

    if (req.query.end_date) {
      filters.end_date = req.query.end_date;
    }

    if (req.query.assigned_employee_id && req.user.role === 'admin') {
      filters.assigned_employee_id = req.query.assigned_employee_id;
    }

    const tasks = await getAllTasks(filters);
    res.json(tasks);
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/tasks/:id', authenticateToken, async (req, res) => {
  try {
    const task = await getTaskById(req.params.id);

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    // Check permissions
    if (req.user.role === 'employee' && task.assigned_employee_id !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json(task);
  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/tasks', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const {
      customer_name,
      customer_phone,
      customer_postcode,
      customer_email,
      service_type,
      service_description,
      start_date,
      estimated_end_date,
      service_fee,
      assigned_employee_id
    } = req.body;

    // Validate required fields
    if (!customer_name || !customer_phone || !customer_postcode || !service_type || !service_description || !start_date || !estimated_end_date) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const taskData = {
      customer_name,
      customer_phone,
      customer_postcode,
      customer_email,
      service_type,
      service_description,
      start_date,
      estimated_end_date,
      service_fee,
      assigned_employee_id,
      created_by: req.user.id,
      status: assigned_employee_id ? 'assigned' : 'pending'
    };

    const result = await createTask(taskData);
    const newTask = await getTaskById(result.id);

    // Generate work sessions for multi-day tasks
    if (taskData.start_date !== taskData.estimated_end_date && taskData.assigned_employee_id) {
      await generateWorkSessions(result.id, taskData.start_date, taskData.estimated_end_date, taskData.assigned_employee_id);
    }

    res.status(201).json(newTask);
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Accept task endpoint for employees
app.put('/api/tasks/:id/accept', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'employee') {
      return res.status(403).json({ error: 'Only employees can accept tasks' });
    }

    const task = await getTaskById(req.params.id);

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    // Check if task is available for acceptance
    if (task.status !== 'pending' || task.assigned_employee_id !== null) {
      return res.status(400).json({ error: 'Task is not available for acceptance' });
    }

    // Accept the task
    const updateData = {
      assigned_employee_id: req.user.id,
      status: 'assigned'
    };

    const result = await updateTask(req.params.id, updateData);
    const updatedTask = await getTaskById(req.params.id);

    // Generate work sessions for multi-day tasks
    if (updatedTask.start_date !== updatedTask.estimated_end_date) {
      await generateWorkSessions(req.params.id, updatedTask.start_date, updatedTask.estimated_end_date, req.user.id);
    }

    res.json(updatedTask);
  } catch (error) {
    console.error('Accept task error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.put('/api/tasks/:id', authenticateToken, async (req, res) => {
  try {
    const task = await getTaskById(req.params.id);

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    // Check permissions
    if (req.user.role === 'employee') {
      // Employees can only update status and actual_end_date of their assigned tasks
      if (task.assigned_employee_id !== req.user.id) {
        return res.status(403).json({ error: 'Access denied' });
      }

      const allowedFields = ['status', 'actual_end_date'];
      const updateData = {};

      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          updateData[field] = req.body[field];
        }
      });

      const result = await updateTask(req.params.id, updateData);
      const updatedTask = await getTaskById(req.params.id);

      res.json(updatedTask);
    } else {
      // Admins can update any field
      const result = await updateTask(req.params.id, req.body);
      const updatedTask = await getTaskById(req.params.id);

      res.json(updatedTask);
    }
  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.delete('/api/tasks/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const result = await deleteTask(req.params.id);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Task not found' });
    }

    res.json({ message: 'Task deleted successfully' });
  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get work sessions for a task
app.get('/api/tasks/:id/sessions', authenticateToken, async (req, res) => {
  try {
    const task = await getTaskById(req.params.id);

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    // Check permissions
    if (req.user.role === 'employee' && task.assigned_employee_id !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const sessions = await getTaskWorkSessions(req.params.id);
    res.json(sessions);
  } catch (error) {
    console.error('Get work sessions error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update work session
app.put('/api/sessions/:id', authenticateToken, async (req, res) => {
  try {
    const result = await updateWorkSession(req.params.id, req.body);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Work session not found' });
    }

    res.json({ message: 'Work session updated successfully' });
  } catch (error) {
    console.error('Update work session error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Employee attendance endpoints
app.get('/api/attendance', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'employee') {
      return res.status(403).json({ error: 'Only employees can check attendance' });
    }

    const workDate = req.query.date || new Date().toISOString().split('T')[0];
    const attendance = await getEmployeeAttendance(req.user.id, workDate);

    res.json(attendance || { status: 'clocked_out', work_date: workDate });
  } catch (error) {
    console.error('Get attendance error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/attendance/clock-in', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'employee') {
      return res.status(403).json({ error: 'Only employees can clock in' });
    }

    const result = await clockIn(req.user.id);
    res.json({ message: 'Clocked in successfully', ...result });
  } catch (error) {
    console.error('Clock in error:', error);
    if (error.message === 'Already clocked in today') {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Internal server error' });
    }
  }
});

app.post('/api/attendance/clock-out', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'employee') {
      return res.status(403).json({ error: 'Only employees can clock out' });
    }

    const result = await clockOut(req.user.id);
    res.json({ message: 'Clocked out successfully', ...result });
  } catch (error) {
    console.error('Clock out error:', error);
    if (error.message === 'Not clocked in today') {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Internal server error' });
    }
  }
});

// Get attendance statistics
app.get('/api/attendance/stats', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'employee') {
      return res.status(403).json({ error: 'Only employees can view attendance stats' });
    }

    const period = req.query.period || 'week'; // 'week' or 'month'
    const stats = await getAttendanceStats(req.user.id, period);
    res.json(stats);
  } catch (error) {
    console.error('Get attendance stats error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get recent attendance records
app.get('/api/attendance/recent', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'employee') {
      return res.status(403).json({ error: 'Only employees can view attendance records' });
    }

    const limit = parseInt(req.query.limit) || 10;
    const records = await getRecentAttendance(req.user.id, limit);
    res.json(records);
  } catch (error) {
    console.error('Get recent attendance error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get daily attendance overview for admin
app.get('/api/admin/attendance/daily', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Only admins can view attendance overview' });
    }

    const workDate = req.query.date || new Date().toISOString().split('T')[0];
    const overview = await getDailyAttendanceOverview(workDate);
    res.json(overview);
  } catch (error) {
    console.error('Get daily attendance overview error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Handyman Management System API' });
});

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Local: http://localhost:${PORT}/api/health`);
  console.log(`Network: http://**************:${PORT}/api/health`);
});

// Keep the server running
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

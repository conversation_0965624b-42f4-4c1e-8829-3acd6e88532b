# Handyman Management System

A lightweight demo management system for UK handyman services built with React 19 and Express.js with SQLite authentication.

## Features

- **Role-based Authentication**: Admin and Employee login system
- **Admin Dashboard**: User management, system overview, and administrative tools
- **Employee Dashboard**: Task management, time tracking, and activity monitoring
- **Responsive Design**: Works on desktop and mobile devices
- **Lightweight**: Simple SQLite database for easy setup and demo purposes

## Demo Accounts

All accounts use the password: `password123`

### Admin Account
- **Username**: `admin`
- **Password**: `password123`
- **Role**: Administrator
- **Full Name**: Administrator

### Employee Accounts
| Username | Password | Full Name |
|----------|----------|-----------|
| tom | password123 | <PERSON> |
| john | password123 | <PERSON> |
| mike | password123 | <PERSON> |
| david | password123 | <PERSON> |
| james | password123 | <PERSON> |
| robert | password123 | <PERSON> |
| william | password123 | <PERSON> |
| richard | password123 | <PERSON> |
| charles | password123 | <PERSON> |
| daniel | password123 | <PERSON> |

## Installation & Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start the Application**
   ```bash
   npm start
   ```
   This will start both the backend server (port 3001) and frontend development server (port 5173).

3. **Access the Application**
   - **Local Access:**
     - Frontend: http://localhost:5173
     - Backend API: http://localhost:3001/api
   - **External Access:**
     - Frontend: http://**************:5173
     - Backend API: http://**************:3001/api

## Project Structure

```
handyman-server/
├── server/
│   ├── index.js          # Express server
│   ├── database.js       # SQLite database setup
│   └── handyman.db       # SQLite database file (auto-generated)
├── src/
│   ├── components/
│   │   ├── Login.jsx
│   │   ├── AdminDashboard.jsx
│   │   └── EmployeeDashboard.jsx
│   ├── utils/
│   │   └── auth.js       # Authentication utilities
│   ├── App.jsx
│   ├── App.css
│   └── main.jsx
├── package.json
└── README.md
```

## API Endpoints

- `POST /api/login` - User authentication
- `GET /api/profile` - Get current user profile (requires auth)
- `GET /api/users` - Get all users (admin only)
- `GET /api/health` - Health check

## Technology Stack

- **Frontend**: React 19, React Router DOM
- **Backend**: Express.js, Node.js
- **Database**: SQLite3
- **Authentication**: JWT (JSON Web Tokens)
- **Password Hashing**: bcryptjs
- **Styling**: CSS3 with responsive design

## Development

### Running Backend Only
```bash
npm run server
```

### Running Frontend Only
```bash
npm run dev
```

### Running Both (Recommended)
```bash
npm start
```

## Security Notes

⚠️ **This is a demo application** - Do not use in production without proper security measures:

- Change default passwords
- Use environment variables for JWT secret
- Implement proper password policies
- Add rate limiting
- Use HTTPS in production
- Implement proper session management

## Database Schema

### Users Table
```sql
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  role TEXT NOT NULL CHECK(role IN ('admin', 'employee')),
  full_name TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Customization

To modify user accounts, edit the `insertDefaultUsers` function in `server/database.js` and delete the `handyman.db` file to regenerate the database.

## License

This is a demo project for educational purposes.

import { useState, useEffect } from 'react'
import { getStoredAuth } from '../utils/auth'
import CreateTaskModal from './CreateTaskModal'

const TaskManagement = () => {
  const [tasks, setTasks] = useState([])
  const [employees, setEmployees] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [filters, setFilters] = useState({
    status: '',
    date: new Date().toISOString().split('T')[0], // 默认今天
    assigned_employee_id: ''
  })
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedTask, setSelectedTask] = useState(null)
  const [showTaskDetail, setShowTaskDetail] = useState(false)
  const [showPhotoModal, setShowPhotoModal] = useState(false)
  const [selectedPhoto, setSelectedPhoto] = useState(null)

  useEffect(() => {
    fetchTasks()
    fetchEmployees()
  }, [filters])

  const fetchTasks = async () => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const queryParams = new URLSearchParams()
      Object.keys(filters).forEach(key => {
        if (filters[key] && key !== 'date') {
          queryParams.append(key, filters[key])
        }
      })

      const response = await fetch(`/api/tasks?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        let data = await response.json()

        // Filter tasks based on date - include multi-day tasks that span the selected date
        if (filters.date) {
          data = data.filter(task => {
            const selectedDate = filters.date
            const taskStartDate = task.start_date
            const taskEndDate = task.estimated_end_date

            // Include task if selected date falls within task duration
            return selectedDate >= taskStartDate && selectedDate <= taskEndDate
          })
        }

        // Auto-cancel overdue tasks
        const today = new Date().toISOString().split('T')[0]
        const updatedTasks = await Promise.all(data.map(async (task) => {
          if (task.estimated_end_date < today &&
              (task.status === 'pending' || task.status === 'assigned' || task.status === 'in_progress')) {
            try {
              const updateResponse = await fetch(`/api/tasks/${task.id}`, {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${auth.token}`
                },
                body: JSON.stringify({ status: 'cancelled' })
              })

              if (updateResponse.ok) {
                return { ...task, status: 'cancelled' }
              }
            } catch (err) {
              console.error('Error auto-cancelling overdue task:', err)
            }
          }
          return task
        }))

        setTasks(updatedTasks)
      } else {
        setError('Failed to fetch tasks')
      }
    } catch (err) {
      setError('Error fetching tasks')
    } finally {
      setLoading(false)
    }
  }

  const fetchEmployees = async () => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setEmployees(data.filter(user => user.role === 'employee'))
      }
    } catch (err) {
      console.error('Error fetching employees:', err)
    }
  }

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getStatusColor = (status, task) => {
    const today = new Date().toISOString().split('T')[0]
    const startDate = task?.start_date

    switch (status) {
      case 'pending':
      case 'assigned':
        // 🔴 未开始/超期 - 如果开始日期已过但未开始工作
        return startDate && startDate < today ? '#dc3545' : '#ffc107'
      case 'in_progress':
        // 🟡 进行中
        return '#ffc107'
      case 'completed':
        // 🟢 已完成
        return '#28a745'
      case 'cancelled':
        // 🔴 已取消
        return '#dc3545'
      case 'skipped':
        // ⚪ 已跳过
        return '#6c757d'
      default: return '#6c757d'
    }
  }

  const getStatusIcon = (status, task) => {
    const today = new Date().toISOString().split('T')[0]
    const startDate = task?.start_date

    switch (status) {
      case 'pending':
      case 'assigned':
        return startDate && startDate < today ? '🔴' : '🟡'
      case 'in_progress':
        return '🟡'
      case 'completed':
        return '🟢'
      case 'cancelled':
        return '🔴'
      case 'skipped':
        return '⚪'
      default: return '⚪'
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString('en-GB')
  }

  const formatCurrency = (amount) => {
    if (!amount) return '£0.00'
    return `£${parseFloat(amount).toFixed(2)}`
  }

  const getTodayTaskStats = () => {
    const today = new Date().toISOString().split('T')[0]

    const todayTasks = tasks.filter(task => {
      const taskStartDate = task.start_date
      const taskEndDate = task.estimated_end_date
      // Include tasks that are scheduled for today or are ongoing
      return taskStartDate <= today && taskEndDate >= today
    })

    const completed = todayTasks.filter(task => task.status === 'completed').length
    const unassigned = todayTasks.filter(task => task.status === 'pending' && !task.assigned_employee_id).length
    const incomplete = todayTasks.filter(task =>
      task.status === 'pending' ||
      task.status === 'assigned' ||
      task.status === 'in_progress'
    ).length

    return { completed, unassigned, incomplete, total: todayTasks.length }
  }

  const handleTaskClick = (task) => {
    setSelectedTask(task)
    setShowTaskDetail(true)
  }

  const handleAssignTask = async (taskId, employeeId) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({
          assigned_employee_id: employeeId,
          status: 'assigned'
        })
      })

      if (response.ok) {
        fetchTasks()
      } else {
        setError('Failed to assign task')
      }
    } catch (err) {
      setError('Error assigning task')
    }
  }

  const handleCompleteTask = async (taskId, isEarlyCompletion = false) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const updateData = {
        status: 'completed',
        actual_end_date: new Date().toISOString().split('T')[0]
      }

      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify(updateData)
      })

      if (response.ok) {
        // If early completion, mark remaining work sessions as skipped
        if (isEarlyCompletion) {
          // This would be implemented to update remaining sessions
          console.log('Early completion - marking remaining days as skipped')
        }

        fetchTasks()
        setShowTaskDetail(false)
      } else {
        setError('Failed to complete task')
      }
    } catch (err) {
      setError('Error completing task')
    }
  }

  if (loading) {
    return <div className="loading">Loading tasks...</div>
  }

  return (
    <div className="task-management">
      {error && <div className="error-message">{error}</div>}
      
      {/* Task Header */}
      <div className="task-header">
        <h2>Task Management</h2>
        <button
          className="action-button primary"
          onClick={() => setShowCreateModal(true)}
        >
          Create New Task
        </button>
      </div>

      {/* Today's Task Statistics */}
      <div className="task-stats">
        <div className="stats-container">
          <div className="stat-item completed">
            <div className="stat-number">{getTodayTaskStats().completed}</div>
            <div className="stat-label">Today Completed</div>
          </div>
          <div className="stat-item unassigned">
            <div className="stat-number">{getTodayTaskStats().unassigned}</div>
            <div className="stat-label">Unassigned</div>
          </div>
          <div className="stat-item incomplete">
            <div className="stat-number">{getTodayTaskStats().incomplete}</div>
            <div className="stat-label">Incomplete</div>
          </div>
          <div className="stat-item total">
            <div className="stat-number">{getTodayTaskStats().total}</div>
            <div className="stat-label">Total Today</div>
          </div>
        </div>
      </div>

      {/* Task Filters */}
      <div className="task-filters">
        <div className="filter-group">
          <label>Status</label>
          <select 
            className="status-filter"
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
          >
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="assigned">Assigned</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <div className="filter-group">
          <label>Date Filter</label>
          <input
            type="date"
            className="date-filter"
            value={filters.date}
            onChange={(e) => handleFilterChange('date', e.target.value)}
            lang="en-US"
          />
        </div>

        <div className="filter-group">
          <label>Assigned Employee</label>
          <select 
            className="employee-filter"
            value={filters.assigned_employee_id}
            onChange={(e) => handleFilterChange('assigned_employee_id', e.target.value)}
          >
            <option value="">All Employees</option>
            {employees.map(employee => (
              <option key={employee.id} value={employee.id}>
                {employee.full_name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Task List */}
      <div className="admin-tasks-container">
        {tasks.length === 0 ? (
          <div className="no-tasks">
            <p>No tasks found matching the current filters.</p>
          </div>
        ) : (
          tasks.map(task => (
            <div
              key={task.id}
              className={`admin-task-card ${task.status}`}
            >
              <div className="task-header-info">
                <h4>{task.service_type} - {task.customer_name}</h4>
                <div className="task-status-container">
                  <span className="status-icon">{getStatusIcon(task.status, task)}</span>
                  <span
                    className="task-status"
                    style={{ backgroundColor: getStatusColor(task.status, task) }}
                  >
                    {task.status.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
              </div>

              <div className="task-details">
                <div className="customer-info">
                  <strong>Customer Details:</strong><br />
                  Name: {task.customer_name}<br />
                  Phone: {task.customer_phone}<br />
                  Postcode: {task.customer_postcode}<br />
                  {task.customer_email && `Email: ${task.customer_email}`}
                </div>

                <div className="service-info">
                  <strong>Service Details:</strong><br />
                  Type: {task.service_type}<br />
                  Description: {task.service_description}<br />
                  Fee: {formatCurrency(task.service_fee)}
                </div>

                <div className="assignment-info">
                  <strong>Schedule & Assignment:</strong><br />
                  Start: {formatDate(task.start_date)}<br />
                  Est. End: {formatDate(task.estimated_end_date)}<br />
                  {task.actual_end_date && `Actual End: ${formatDate(task.actual_end_date)}`}<br />
                  Assigned: {task.assigned_employee_name || 'Unassigned'}
                </div>
              </div>

              <div className="task-actions" onClick={(e) => e.stopPropagation()}>
                {task.status === 'pending' && (
                  <select 
                    className="btn-assign"
                    onChange={(e) => {
                      if (e.target.value) {
                        handleAssignTask(task.id, e.target.value)
                      }
                    }}
                    defaultValue=""
                  >
                    <option value="">Assign to...</option>
                    {employees.map(employee => (
                      <option key={employee.id} value={employee.id}>
                        {employee.full_name}
                      </option>
                    ))}
                  </select>
                )}
                
                {(task.status === 'in_progress' || task.status === 'assigned') && (
                  <button 
                    className="btn-view"
                    onClick={() => handleCompleteTask(task.id)}
                  >
                    Mark Complete
                  </button>
                )}
                
                <button
                  className="btn-view"
                  onClick={() => handleTaskClick(task)}
                >
                  View Details
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Task Detail Modal */}
      {showTaskDetail && selectedTask && (
        <TaskDetailModal
          task={selectedTask}
          employees={employees}
          onClose={() => setShowTaskDetail(false)}
          onUpdate={fetchTasks}
        />
      )}

      {/* Create Task Modal */}
      {showCreateModal && (
        <CreateTaskModal
          employees={employees}
          onClose={() => setShowCreateModal(false)}
          onSuccess={fetchTasks}
        />
      )}

      {/* Photo View Modal */}
      {showPhotoModal && selectedPhoto && (
        <div className="modal-overlay" onClick={() => setShowPhotoModal(false)}>
          <div className="photo-modal" onClick={(e) => e.stopPropagation()}>
            <button className="close-button-fixed" onClick={() => setShowPhotoModal(false)}>×</button>
            <div className="photo-modal-content">
              <div className="photo-display">
                {selectedPhoto.file ? (
                  <img
                    src={URL.createObjectURL(selectedPhoto.file)}
                    alt={selectedPhoto.file_name}
                    className="full-photo"
                  />
                ) : (
                  <div className="photo-placeholder-large">
                    📷 {selectedPhoto.file_name || 'Photo'}
                  </div>
                )}
              </div>
              <div className="photo-details">
                <h4>Photo Details</h4>
                <p><strong>File:</strong> {selectedPhoto.file_name}</p>
                <p><strong>Uploaded:</strong> {selectedPhoto.timestamp}</p>
                {selectedPhoto.uploaded_at && (
                  <p><strong>Upload Time:</strong> {new Date(selectedPhoto.uploaded_at).toLocaleString()}</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Task Detail Modal with timeline and photos
const TaskDetailModal = ({ task, employees, onClose, onUpdate }) => {
  const [workSessions, setWorkSessions] = useState([])
  const [photos, setPhotos] = useState([])
  const [isEditing, setIsEditing] = useState(false)
  const [currentTask, setCurrentTask] = useState(task)
  const [editFormData, setEditFormData] = useState({
    customer_name: task.customer_name,
    customer_phone: task.customer_phone,
    customer_postcode: task.customer_postcode,
    customer_email: task.customer_email || '',
    service_type: task.service_type,
    service_description: task.service_description,
    start_date: task.start_date,
    estimated_end_date: task.estimated_end_date,
    service_fee: task.service_fee || '',
    assigned_employee_id: task.assigned_employee_id || ''
  })

  // Update form data when task changes
  useEffect(() => {
    setCurrentTask(task)
    setEditFormData({
      customer_name: task.customer_name,
      customer_phone: task.customer_phone,
      customer_postcode: task.customer_postcode,
      customer_email: task.customer_email || '',
      service_type: task.service_type,
      service_description: task.service_description,
      start_date: task.start_date,
      estimated_end_date: task.estimated_end_date,
      service_fee: task.service_fee || '',
      assigned_employee_id: task.assigned_employee_id || ''
    })
  }, [task])

  // Generate work sessions based on start and end dates
  const generateWorkSessions = () => {
    const sessions = []
    const startDate = new Date(currentTask.start_date)
    const endDate = new Date(currentTask.estimated_end_date)

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0]
      const today = new Date().toISOString().split('T')[0]

      // Create session structure - actual data will come from API
      const session = {
        id: sessions.length + 1,
        work_date: dateStr,
        start_photos: [],
        end_photos: [],
        start_time: null,
        end_time: null,
        total_hours: 0,
        work_summary: '',
        status: dateStr < today ? 'completed' : (dateStr === today ? 'in_progress' : 'pending')
      }

      sessions.push(session)
    }

    return sessions
  }

  const workSessionsData = generateWorkSessions()

  const getTotalWorkHours = () => {
    return workSessionsData.reduce((total, session) => total + session.total_hours, 0)
  }

  const calculateWorkHours = (startPhotos, endPhotos) => {
    if (!startPhotos.length || !endPhotos.length) return 0

    const startTime = new Date(startPhotos[0].uploaded_at)
    const endTime = new Date(endPhotos[endPhotos.length - 1].uploaded_at)

    return Math.round((endTime - startTime) / (1000 * 60 * 60) * 100) / 100 // Round to 2 decimal places
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return '🟢'
      case 'in_progress': return '🟡'
      case 'skipped': return '⚪'
      default: return '🔴'
    }
  }

  const handlePhotoUpload = (sessionId, photoType, files) => {
    // Handle photo upload logic here
    console.log(`Uploading ${photoType} photos for session ${sessionId}:`, files)
    // In real app, would upload to server and update session data
  }

  const handlePhotoClick = (photo) => {
    setSelectedPhoto(photo)
    setShowPhotoModal(true)
  }

  const handleWorkSummaryChange = async (sessionId, workSummary) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/sessions/${sessionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({
          notes: workSummary
        })
      })

      if (response.ok) {
        console.log('Work summary updated successfully')
      } else {
        console.error('Failed to update work summary')
      }
    } catch (err) {
      console.error('Error updating work summary:', err)
    }
  }

  const handleEditSave = async () => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${currentTask.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify(editFormData)
      })

      if (response.ok) {
        // Fetch the updated task data
        const updatedTaskResponse = await fetch(`/api/tasks/${currentTask.id}`, {
          headers: {
            'Authorization': `Bearer ${auth.token}`
          }
        })

        if (updatedTaskResponse.ok) {
          const updatedTask = await updatedTaskResponse.json()
          setCurrentTask(updatedTask)
          setIsEditing(false)

          // Refresh the parent component
          onUpdate()
        }
      } else {
        console.error('Failed to update task')
      }
    } catch (err) {
      console.error('Error updating task:', err)
    }
  }

  const handleDeleteTask = async () => {
    if (!confirm('Are you sure you want to cancel this task? This action cannot be undone.')) {
      return
    }

    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${currentTask.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({ status: 'cancelled' })
      })

      if (response.ok) {
        onUpdate()
        onClose()
      } else {
        console.error('Failed to cancel task')
      }
    } catch (err) {
      console.error('Error cancelling task:', err)
    }
  }

  const handleRestartTask = async () => {
    if (!confirm('Are you sure you want to restart this task? This will reset its status to in progress.')) {
      return
    }

    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${currentTask.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({
          status: 'in_progress',
          actual_end_date: null
        })
      })

      if (response.ok) {
        // Fetch the updated task data
        const updatedTaskResponse = await fetch(`/api/tasks/${currentTask.id}`, {
          headers: {
            'Authorization': `Bearer ${auth.token}`
          }
        })

        if (updatedTaskResponse.ok) {
          const updatedTask = await updatedTaskResponse.json()
          setCurrentTask(updatedTask)
          onUpdate()
        }
      } else {
        console.error('Failed to restart task')
      }
    } catch (err) {
      console.error('Error restarting task:', err)
    }
  }

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content task-detail-modal-large" onClick={(e) => e.stopPropagation()}>
        {/* Close button in top-right corner */}
        <button className="close-button-fixed" onClick={onClose}>×</button>

        <div className="modal-header-simple">
          <h3>{getStatusIcon(currentTask.status)} {currentTask.service_type} - {currentTask.customer_name}</h3>
        </div>

        <div className="modal-body">
          {/* Basic Information Grid */}
          {!isEditing ? (
            <div className="task-detail-grid">
              <div className="detail-section">
                <h4>👤 Customer Information</h4>
                <p><strong>Name:</strong> {currentTask.customer_name}</p>
                <p><strong>Phone:</strong> {currentTask.customer_phone}</p>
                <p><strong>Postcode:</strong> {currentTask.customer_postcode}</p>
                {currentTask.customer_email && <p><strong>Email:</strong> {currentTask.customer_email}</p>}
              </div>

              <div className="detail-section">
                <h4>🔧 Service Details</h4>
                <p><strong>Type:</strong> {currentTask.service_type}</p>
                <p><strong>Description:</strong> {currentTask.service_description}</p>
                <p><strong>Fee:</strong> {currentTask.service_fee ? `£${parseFloat(currentTask.service_fee).toFixed(2)}` : 'Not set'}</p>
              </div>

              <div className="detail-section">
                <h4>📅 Schedule & Assignment</h4>
                <p><strong>Start Date:</strong> {new Date(currentTask.start_date).toLocaleDateString('en-GB')}</p>
                <p><strong>Est. End Date:</strong> {new Date(currentTask.estimated_end_date).toLocaleDateString('en-GB')}</p>
                {currentTask.actual_end_date && (
                  <p><strong>Actual End:</strong> {new Date(currentTask.actual_end_date).toLocaleDateString('en-GB')}</p>
                )}
                <p><strong>Assigned Employee:</strong> {currentTask.assigned_employee_name || 'Unassigned'}</p>
                <p><strong>Total Work Hours:</strong> {getTotalWorkHours()} hours (Actual)</p>
              </div>
            </div>
          ) : (
            <div className="edit-form">
              <h4>Edit Task Information</h4>
              <div className="form-grid">
                <div className="form-group">
                  <label>Customer Name *</label>
                  <input
                    type="text"
                    value={editFormData.customer_name}
                    onChange={(e) => setEditFormData({...editFormData, customer_name: e.target.value})}
                  />
                </div>
                <div className="form-group">
                  <label>Phone Number *</label>
                  <input
                    type="text"
                    value={editFormData.customer_phone}
                    onChange={(e) => setEditFormData({...editFormData, customer_phone: e.target.value})}
                  />
                </div>
                <div className="form-group">
                  <label>Postcode *</label>
                  <input
                    type="text"
                    value={editFormData.customer_postcode}
                    onChange={(e) => setEditFormData({...editFormData, customer_postcode: e.target.value})}
                  />
                </div>
                <div className="form-group">
                  <label>Email</label>
                  <input
                    type="email"
                    value={editFormData.customer_email}
                    onChange={(e) => setEditFormData({...editFormData, customer_email: e.target.value})}
                  />
                </div>
                <div className="form-group">
                  <label>Service Type *</label>
                  <input
                    type="text"
                    value={editFormData.service_type}
                    onChange={(e) => setEditFormData({...editFormData, service_type: e.target.value})}
                  />
                </div>
                <div className="form-group">
                  <label>Service Fee (£)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={editFormData.service_fee}
                    onChange={(e) => setEditFormData({...editFormData, service_fee: e.target.value})}
                  />
                </div>
                <div className="form-group">
                  <label>Start Date *</label>
                  <input
                    type="date"
                    value={editFormData.start_date}
                    onChange={(e) => setEditFormData({...editFormData, start_date: e.target.value})}
                  />
                </div>
                <div className="form-group">
                  <label>Estimated End Date *</label>
                  <input
                    type="date"
                    value={editFormData.estimated_end_date}
                    onChange={(e) => setEditFormData({...editFormData, estimated_end_date: e.target.value})}
                  />
                </div>
                <div className="form-group">
                  <label>Assigned Employee</label>
                  <select
                    value={editFormData.assigned_employee_id}
                    onChange={(e) => setEditFormData({...editFormData, assigned_employee_id: e.target.value})}
                  >
                    <option value="">Unassigned</option>
                    {employees.map(employee => (
                      <option key={employee.id} value={employee.id}>
                        {employee.full_name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="form-group">
                <label>Service Description *</label>
                <textarea
                  value={editFormData.service_description}
                  onChange={(e) => setEditFormData({...editFormData, service_description: e.target.value})}
                  rows="3"
                />
              </div>
            </div>
          )}

          {/* Work Timeline */}
          {!isEditing && (
            <div className="timeline-section">
              <h4>📊 Work Timeline & Photo Documentation</h4>
              <div className="work-timeline">
                {workSessionsData.map((session, index) => {
                  const isToday = session.work_date === new Date().toISOString().split('T')[0]
                  const canEdit = isToday && (currentTask.status === 'in_progress' || currentTask.status === 'assigned')

                  return (
                    <div key={session.id} className={`timeline-day ${session.status} ${canEdit ? 'today-editable' : ''}`}>
                      <div className="day-header">
                        <h5>
                          {getStatusIcon(session.status)} Day {index + 1} - {new Date(session.work_date).toLocaleDateString('en-GB')}
                          {isToday && <span className="today-badge">Today</span>}
                        </h5>
                      <div className="day-summary">
                        <span className="work-hours">{session.total_hours}h</span>
                        {session.start_time && session.end_time && (
                          <span className="work-time">{session.start_time} - {session.end_time}</span>
                        )}
                      </div>
                    </div>

                    {/* Work Content Input */}
                    <div className="work-content-section">
                      <h6>📝 Work Content</h6>
                      {session.status === 'in_progress' || session.status === 'completed' ? (
                        <div className="work-content-input">
                          <textarea
                            placeholder="Describe the work completed today..."
                            value={session.notes || ''}
                            onChange={(e) => handleWorkSummaryChange(session.id, e.target.value)}
                            rows="3"
                            className="work-summary-textarea"
                          />
                          <div className="work-content-info">
                            <small>💡 Tip: Include details about tasks completed, materials used, and any issues encountered.</small>
                          </div>
                        </div>
                      ) : (
                        <div className="work-content-placeholder">
                          <p>Work content will be available when work begins.</p>
                        </div>
                      )}
                    </div>

                    {/* Photo Documentation */}
                    <div className="photo-documentation">
                      <div className="photo-section">
                        <h6>📷 Start Photos</h6>
                        <div className="photo-grid">
                          {session.start_photos.map((photo, photoIndex) => (
                            <div key={photoIndex} className="photo-item clickable" onClick={() => handlePhotoClick(photo)}>
                              {photo.file ? (
                                <div className="photo-thumbnail">
                                  <img
                                    src={URL.createObjectURL(photo.file)}
                                    alt={`Start photo ${photoIndex + 1}`}
                                    className="thumbnail-image"
                                  />
                                </div>
                              ) : (
                                <div className="photo-placeholder">
                                  📷 START {photoIndex + 1}
                                </div>
                              )}
                              <div className="photo-info">
                                <span className="photo-time">{photo.timestamp}</span>
                              </div>
                              <div className="photo-overlay">
                                <span className="view-icon">👁️</span>
                              </div>
                            </div>
                          ))}
                          {session.status === 'in_progress' && session.start_photos.length === 0 && (
                            <div className="photo-upload-area">
                              <label>Upload Start Photos</label>
                              <input
                                type="file"
                                multiple
                                accept="image/*"
                                onChange={(e) => handlePhotoUpload(session.id, 'start', e.target.files)}
                              />
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="photo-section">
                        <h6>📷 End Photos</h6>
                        <div className="photo-grid">
                          {session.end_photos.map((photo, photoIndex) => (
                            <div key={photoIndex} className="photo-item clickable" onClick={() => handlePhotoClick(photo)}>
                              {photo.file ? (
                                <div className="photo-thumbnail">
                                  <img
                                    src={URL.createObjectURL(photo.file)}
                                    alt={`End photo ${photoIndex + 1}`}
                                    className="thumbnail-image"
                                  />
                                </div>
                              ) : (
                                <div className="photo-placeholder">
                                  📷 END {photoIndex + 1}
                                </div>
                              )}
                              <div className="photo-info">
                                <span className="photo-time">{photo.timestamp}</span>
                              </div>
                              <div className="photo-overlay">
                                <span className="view-icon">👁️</span>
                              </div>
                            </div>
                          ))}
                          {session.status === 'in_progress' && session.end_photos.length === 0 && (
                            <div className="photo-upload-area">
                              <label>Upload End Photos</label>
                              <input
                                type="file"
                                multiple
                                accept="image/*"
                                onChange={(e) => handlePhotoUpload(session.id, 'end', e.target.files)}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )}
        </div>

        <div className="modal-footer">
          <button className="btn-cancel" onClick={onClose}>Close</button>

          {!isEditing ? (
            <>
              <button className="btn-edit" onClick={() => setIsEditing(true)}>Edit Task</button>

              {/* Delete/Cancel Task Button */}
              {(currentTask.status === 'pending' || currentTask.status === 'assigned' || currentTask.status === 'in_progress') && (
                <button className="btn-delete" onClick={handleDeleteTask}>
                  Cancel Task
                </button>
              )}

              {/* Restart Task Button */}
              {(currentTask.status === 'completed' || currentTask.status === 'cancelled') && (
                <button className="btn-restart" onClick={handleRestartTask}>
                  Restart Task
                </button>
              )}

              {/* Complete Task Button */}
              {currentTask.status === 'in_progress' && (
                <button
                  className="btn-complete"
                  onClick={() => {
                    const isEarlyCompletion = new Date() < new Date(currentTask.estimated_end_date)
                    handleCompleteTask(currentTask.id, isEarlyCompletion)
                  }}
                >
                  {new Date() < new Date(currentTask.estimated_end_date) ? 'Complete Early' : 'Mark as Complete'}
                </button>
              )}
            </>
          ) : (
            <>
              <button className="btn-cancel" onClick={() => setIsEditing(false)}>Cancel</button>
              <button className="btn-create" onClick={handleEditSave}>Save Changes</button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default TaskManagement

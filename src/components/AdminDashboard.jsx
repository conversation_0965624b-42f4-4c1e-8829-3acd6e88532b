import { useState, useEffect } from 'react'
import TaskManagement from './TaskManagement'
import { fetchWithAuth } from '../utils/auth'

const AdminDashboard = ({ user, onLogout }) => {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('overview')
  const [attendanceOverview, setAttendanceOverview] = useState([])
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [attendanceLoading, setAttendanceLoading] = useState(false)
  const [showEditUserModal, setShowEditUserModal] = useState(false)
  const [editingUser, setEditingUser] = useState(null)

  useEffect(() => {
    fetchUsers()
    fetchAttendanceOverview()
  }, [selectedDate])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const response = await fetchWithAuth('/users')

      if (!response) return // Redirected to login

      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }

      const usersData = await response.json()
      setUsers(usersData)
    } catch (error) {
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const fetchAttendanceOverview = async () => {
    try {
      setAttendanceLoading(true)
      const response = await fetchWithAuth(`/admin/attendance/daily?date=${selectedDate}`)

      if (!response) return // Redirected to login

      if (!response.ok) {
        throw new Error('Failed to fetch attendance overview')
      }

      const attendanceData = await response.json()
      setAttendanceOverview(attendanceData)
    } catch (error) {
      console.error('Error fetching attendance overview:', error)
    } finally {
      setAttendanceLoading(false)
    }
  }

  const handleDashboardClick = () => {
    setActiveTab('overview')
  }

  const handleEditUser = (user) => {
    setEditingUser(user)
    setShowEditUserModal(true)
  }

  const handleUpdateUser = async (userData) => {
    try {
      const response = await fetchWithAuth(`/users/${editingUser.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      })

      if (!response) return // Redirected to login

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update user')
      }

      // Refresh users list
      await fetchUsers()
      setShowEditUserModal(false)
      setEditingUser(null)
    } catch (error) {
      console.error('Error updating user:', error)
      setError(error.message)
    }
  }

  const renderTaskManagement = () => {
    return <TaskManagement />
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'tasks':
        return renderTaskManagement()
      case 'statistics':
        return (
          <div className="dashboard-section">
            <h2>Statistics</h2>
            <p>Statistics and reports coming soon...</p>
          </div>
        )
      case 'archive':
        return (
          <div className="dashboard-section">
            <h2>Archive</h2>
            <p>Archive functionality coming soon...</p>
          </div>
        )
      default:
        return (
          <>
            <div className="dashboard-section">
              <h2>System Overview</h2>
              <div className="stats-grid">
                <div className="stat-card">
                  <h3>Total Users</h3>
                  <p className="stat-number">{users.length}</p>
                </div>
                <div className="stat-card">
                  <h3>Employees</h3>
                  <p className="stat-number">{users.filter(u => u.role === 'employee').length}</p>
                </div>
                <div className="stat-card">
                  <h3>Admins</h3>
                  <p className="stat-number">{users.filter(u => u.role === 'admin').length}</p>
                </div>
              </div>
            </div>

            <div className="dashboard-section">
              <div className="attendance-header">
                <h2>Daily Attendance Overview</h2>
                <div className="date-selector">
                  <label htmlFor="attendance-date">Date:</label>
                  <input
                    id="attendance-date"
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                  />
                </div>
              </div>

              {attendanceLoading ? (
                <p>Loading attendance data...</p>
              ) : (
                <div className="attendance-overview">
                  <div className="attendance-summary">
                    <div className="summary-card clocked-in">
                      <h4>🟢 Clocked In</h4>
                      <p className="summary-number">
                        {attendanceOverview.filter(emp => emp.status === 'clocked_in').length}
                      </p>
                    </div>
                    <div className="summary-card clocked-out">
                      <h4>🔴 Not Clocked In</h4>
                      <p className="summary-number">
                        {attendanceOverview.filter(emp => emp.status === 'not_clocked_in').length}
                      </p>
                    </div>
                    <div className="summary-card completed">
                      <h4>✅ Completed Day</h4>
                      <p className="summary-number">
                        {attendanceOverview.filter(emp => emp.status === 'clocked_out' && emp.clock_out_time).length}
                      </p>
                    </div>
                  </div>

                  <div className="attendance-list">
                    <h3>Employee Status</h3>
                    <div className="employee-status-grid">
                      {attendanceOverview.map(emp => (
                        <div key={emp.employee_id} className={`employee-status-card ${emp.status}`}>
                          <div className="employee-name">
                            <strong>{emp.employee_name}</strong>
                          </div>
                          <div className="employee-work-status">
                            <span className={`status-indicator ${emp.status}`}>
                              {emp.status === 'clocked_in' && '🟢 Working'}
                              {emp.status === 'clocked_out' && emp.clock_out_time && '✅ Rest'}
                              {emp.status === 'not_clocked_in' && '🔴 Rest'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="dashboard-section">
              <h2>User Management</h2>
              {loading ? (
                <p>Loading users...</p>
              ) : error ? (
                <p className="error-message">Error: {error}</p>
              ) : (
                <div className="users-table-container">
                  <table className="users-table">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>Username</th>
                        <th>Full Name</th>
                        <th>Role</th>
                        <th>Created</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {users.map(user => (
                        <tr key={user.id}>
                          <td>{user.id}</td>
                          <td>{user.username}</td>
                          <td>{user.full_name}</td>
                          <td>
                            <span className={`role-badge ${user.role}`}>
                              {user.role}
                            </span>
                          </td>
                          <td>{new Date(user.created_at).toLocaleDateString()}</td>
                          <td>
                            <button
                              className="edit-user-button"
                              onClick={() => handleEditUser(user)}
                            >
                              Edit
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            <div className="dashboard-section">
              <h2>Quick Actions</h2>
              <div className="actions-grid">
                <button className="action-button">Add New Employee</button>
                <button className="action-button">View Reports</button>
                <button className="action-button">System Settings</button>
                <button className="action-button">Backup Data</button>
              </div>
            </div>
          </>
        )
    }
  }

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1 className="dashboard-title" onClick={handleDashboardClick}>Dashboard</h1>
        <div className="user-info">
          <span>Welcome, {user.full_name}</span>
          <button onClick={onLogout} className="logout-button">Logout</button>
        </div>
      </header>

      <nav className="dashboard-nav">
        <button
          className={`nav-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`nav-button ${activeTab === 'tasks' ? 'active' : ''}`}
          onClick={() => setActiveTab('tasks')}
        >
          Tasks
        </button>
        <button
          className={`nav-button ${activeTab === 'statistics' ? 'active' : ''}`}
          onClick={() => setActiveTab('statistics')}
        >
          Statistics
        </button>
        <button
          className={`nav-button ${activeTab === 'archive' ? 'active' : ''}`}
          onClick={() => setActiveTab('archive')}
        >
          Archive
        </button>
      </nav>

      <main className="dashboard-content">
        {renderContent()}
      </main>

      {/* Edit User Modal */}
      {showEditUserModal && editingUser && (
        <EditUserModal
          user={editingUser}
          onClose={() => {
            setShowEditUserModal(false)
            setEditingUser(null)
          }}
          onUpdate={handleUpdateUser}
        />
      )}
    </div>
  )
}

// Edit User Modal Component
const EditUserModal = ({ user, onClose, onUpdate }) => {
  const [formData, setFormData] = useState({
    username: user.username,
    full_name: user.full_name,
    role: user.role,
    password: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const updateData = {
        username: formData.username,
        full_name: formData.full_name,
        role: formData.role
      }

      // Only include password if it's provided
      if (formData.password.trim()) {
        updateData.password = formData.password
      }

      await onUpdate(updateData)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <button className="close-button-fixed" onClick={onClose}>×</button>

        <div className="modal-header">
          <h3>Edit User</h3>
        </div>

        <form onSubmit={handleSubmit} className="edit-user-form">
          {error && <div className="error-message">{error}</div>}

          <div className="form-group">
            <label htmlFor="username">Username:</label>
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="full_name">Full Name:</label>
            <input
              type="text"
              id="full_name"
              name="full_name"
              value={formData.full_name}
              onChange={handleChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="role">Role:</label>
            <select
              id="role"
              name="role"
              value={formData.role}
              onChange={handleChange}
              required
            >
              <option value="employee">Employee</option>
              <option value="admin">Admin</option>
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="password">New Password (leave blank to keep current):</label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Enter new password or leave blank"
            />
          </div>

          <div className="modal-footer">
            <button type="button" onClick={onClose} className="cancel-button">
              Cancel
            </button>
            <button type="submit" disabled={loading} className="save-button">
              {loading ? 'Updating...' : 'Update User'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default AdminDashboard

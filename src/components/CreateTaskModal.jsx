import { useState } from 'react'
import { getStoredAuth } from '../utils/auth'

const CreateTaskModal = ({ employees, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    customer_name: '',
    customer_phone: '',
    customer_postcode: '',
    customer_email: '',
    service_type: '',
    service_description: '',
    start_date: '',
    estimated_end_date: '',
    service_fee: '',
    assigned_employee_id: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const serviceTypes = [
    'Plumbing',
    'Electrical',
    'Carpentry',
    'Painting & Decorating',
    'General Maintenance',
    'Heating & Cooling',
    'Flooring',
    'Roofing',
    'Garden & Landscaping',
    'Cleaning Services'
  ]

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateForm = () => {
    const required = [
      'customer_name',
      'customer_phone', 
      'customer_postcode',
      'service_type',
      'service_description',
      'start_date',
      'estimated_end_date'
    ]

    for (const field of required) {
      if (!formData[field]) {
        setError(`${field.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} is required`)
        return false
      }
    }

    // Validate UK phone number format
    const phoneRegex = /^(\+44|0)[1-9]\d{8,9}$/
    if (!phoneRegex.test(formData.customer_phone.replace(/\s/g, ''))) {
      setError('Please enter a valid UK phone number')
      return false
    }

    // Validate UK postcode format
    const postcodeRegex = /^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$/i
    if (!postcodeRegex.test(formData.customer_postcode)) {
      setError('Please enter a valid UK postcode')
      return false
    }

    // Validate email if provided
    if (formData.customer_email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(formData.customer_email)) {
        setError('Please enter a valid email address')
        return false
      }
    }

    // Validate dates
    const startDate = new Date(formData.start_date)
    const endDate = new Date(formData.estimated_end_date)
    if (endDate < startDate) {
      setError('End date cannot be before start date')
      return false
    }

    // Validate service fee if provided
    if (formData.service_fee && (isNaN(formData.service_fee) || parseFloat(formData.service_fee) < 0)) {
      setError('Service fee must be a valid positive number')
      return false
    }

    return true
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      const auth = getStoredAuth()
      if (!auth) {
        setError('Authentication required')
        return
      }

      const submitData = {
        ...formData,
        service_fee: formData.service_fee ? parseFloat(formData.service_fee) : null,
        assigned_employee_id: formData.assigned_employee_id || null
      }

      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify(submitData)
      })

      if (response.ok) {
        onSuccess()
        onClose()
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to create task')
      }
    } catch (err) {
      setError('Error creating task')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content create-task-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Create New Task</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            {error && <div className="error-message">{error}</div>}
            
            {/* Customer Information */}
            <div className="form-section">
              <h4>Customer Information</h4>
              <div className="form-grid">
                <div className="form-group">
                  <label>Customer Name *</label>
                  <input
                    type="text"
                    value={formData.customer_name}
                    onChange={(e) => handleInputChange('customer_name', e.target.value)}
                    placeholder="Enter customer full name"
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label>Phone Number *</label>
                  <input
                    type="tel"
                    value={formData.customer_phone}
                    onChange={(e) => handleInputChange('customer_phone', e.target.value)}
                    placeholder="+44 7700 900123 or 07700 900123"
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label>Postcode *</label>
                  <input
                    type="text"
                    value={formData.customer_postcode}
                    onChange={(e) => handleInputChange('customer_postcode', e.target.value.toUpperCase())}
                    placeholder="SW1A 1AA"
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label>Email Address</label>
                  <input
                    type="email"
                    value={formData.customer_email}
                    onChange={(e) => handleInputChange('customer_email', e.target.value)}
                    placeholder="<EMAIL> (optional)"
                  />
                </div>
              </div>
            </div>

            {/* Service Information */}
            <div className="form-section">
              <h4>Service Details</h4>
              <div className="form-grid">
                <div className="form-group">
                  <label>Service Type *</label>
                  <select
                    value={formData.service_type}
                    onChange={(e) => handleInputChange('service_type', e.target.value)}
                    required
                  >
                    <option value="">Select service type</option>
                    {serviceTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>
                
                <div className="form-group">
                  <label>Service Fee (£)</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.service_fee}
                    onChange={(e) => handleInputChange('service_fee', e.target.value)}
                    placeholder="0.00"
                  />
                </div>
              </div>
              
              <div className="form-group">
                <label>Service Description *</label>
                <textarea
                  value={formData.service_description}
                  onChange={(e) => handleInputChange('service_description', e.target.value)}
                  placeholder="Describe the work to be done in detail..."
                  rows="3"
                  required
                />
              </div>
            </div>

            {/* Schedule & Assignment */}
            <div className="form-section">
              <h4>Schedule & Assignment</h4>
              <div className="form-grid">
                <div className="form-group">
                  <label>Start Date *</label>
                  <input
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => handleInputChange('start_date', e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    lang="en-US"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>Estimated End Date *</label>
                  <input
                    type="date"
                    value={formData.estimated_end_date}
                    onChange={(e) => handleInputChange('estimated_end_date', e.target.value)}
                    min={formData.start_date || new Date().toISOString().split('T')[0]}
                    lang="en-US"
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label>Assign to Employee</label>
                  <select
                    value={formData.assigned_employee_id}
                    onChange={(e) => handleInputChange('assigned_employee_id', e.target.value)}
                  >
                    <option value="">Leave unassigned</option>
                    {employees.map(employee => (
                      <option key={employee.id} value={employee.id}>
                        {employee.full_name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
          
          <div className="modal-footer">
            <button type="button" className="btn-cancel" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn-create" disabled={loading}>
              {loading ? 'Creating...' : 'Create Task'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default CreateTaskModal
